from activities.models import ActivityLog, ActivityType
from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from base.models import Department


class ActivityLogCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating activity logs with flexible activity_highlight support"""

    # Optional generic foreign key fields for highlight
    highlight_content_type = serializers.PrimaryKeyRelatedField(
        queryset=ContentType.objects.all(),
        required=False,
        allow_null=True
    )
    highlight_object_id = serializers.IntegerField(required=False, allow_null=True)

    # Optional generic foreign key fields for destination
    destination_content_type = serializers.PrimaryKeyRelatedField(
        queryset=ContentType.objects.all(),
        required=False,
        allow_null=True
    )
    destination_object_id = serializers.IntegerField(required=False, allow_null=True)

    # Content type and object ID for the incident
    content_type = serializers.PrimaryKeyRelatedField(
        queryset=ContentType.objects.all(),
        required=True
    )
    object_id = serializers.IntegerField(required=True)

    # Activity type from choices
    activity_type = serializers.ChoiceField(choices=ActivityType.choices, required=True)

    # Files as JSON field
    files = serializers.JSONField(required=False, allow_null=True)

    class Meta:
        model = ActivityLog
        fields = [
            'user',
            'activity_type',
            'description',
            'content_type',
            'object_id',
            'highlight_content_type',
            'highlight_object_id',
            'destination_content_type',
            'destination_object_id',
            'files',
        ]

    def validate(self, data):
        """Validate that highlight and destination fields are provided together"""
        highlight_content_type = data.get('highlight_content_type')
        highlight_object_id = data.get('highlight_object_id')
        destination_content_type = data.get('destination_content_type')
        destination_object_id = data.get('destination_object_id')

        if highlight_content_type and not highlight_object_id:
            raise serializers.ValidationError(
                "highlight_object_id is required when highlight_content_type is provided"
            )
        if highlight_object_id and not highlight_content_type:
            raise serializers.ValidationError(
                "highlight_content_type is required when highlight_object_id is provided"
            )

        if destination_content_type and not destination_object_id:
            raise serializers.ValidationError(
                "destination_object_id is required when destination_content_type is provided"
            )
        if destination_object_id and not destination_content_type:
            raise serializers.ValidationError(
                "destination_content_type is required when destination_object_id is provided"
            )

        return data


class ActivityLogListSerializer(serializers.ModelSerializer):
    """Serializer for listing activity logs with the exact response structure required"""

    incident = serializers.SerializerMethodField()
    activity_message = serializers.SerializerMethodField()
    activity_highlight = serializers.SerializerMethodField()
    activity_date = serializers.DateTimeField(source='timestamp')
    destination = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    files = serializers.JSONField()

    class Meta:
        model = ActivityLog
        fields = [
            'id',
            'incident',
            'activity_message',
            'activity_highlight',
            'activity_date',
            'destination',
            'files',
            'created_by',
            'activity_type',
        ]

    def get_incident(self, obj):
        """Return the incident ID"""
        return obj.object_id

    def get_activity_message(self, obj):
        """Generate activity message based on activity type and context"""
        if obj.description:
            return obj.description

        # Generate default messages based on activity type
        message_map = {
            ActivityType.DOCUMENT_ADDED: self._get_document_message(obj),
            ActivityType.SENT_TO_DEPARTMENT: "incident sent to",
            ActivityType.REVIEW_ADDED: "review added",
            ActivityType.UPDATED: "incident updated",
            ActivityType.CREATED: "incident created",
            ActivityType.STATUS_CHANGED: "status changed",
            ActivityType.ASSIGNED: "incident assigned",
            ActivityType.RESOLVED: "incident resolved",
        }

        return message_map.get(obj.activity_type, f"{obj.activity_type} performed")

    def _get_document_message(self, obj):
        """Generate document message based on files count"""
        if obj.files and isinstance(obj.files, list):
            count = len(obj.files)
            return f"{count} incoming document{'s' if count != 1 else ''} uploaded"
        return "document uploaded"

    def get_activity_highlight(self, obj):
        """Return activity highlight as dictionary"""
        if obj.activity_highlight:
            return self._format_object_for_response(obj.activity_highlight)
        return {}

    def get_destination(self, obj):
        """Return destination information"""
        if obj.activity_destination:
            return self._format_object_for_response(obj.activity_destination)
        elif obj.activity_highlight:
            return self._format_object_for_response(obj.activity_highlight)
        return None

    def get_created_by(self, obj):
        """Return created_by user information"""
        if obj.user:
            return {
                "id": obj.user.id,
                "first_name": obj.user.first_name,
                "last_name": obj.user.last_name
            }
        return {}

    def _format_object_for_response(self, obj):
        """Format different object types for consistent response structure"""
        from base.models import Department, Facility

        if hasattr(obj, 'first_name'):  # User
            return {
                "user": {
                    "id": obj.id,
                    "first_name": obj.first_name,
                    "last_name": obj.last_name
                }
            }
        elif isinstance(obj, Department):
            return {
                "department": {
                    "id": obj.id,
                    "name": obj.name
                }
            }
        elif isinstance(obj, Facility):
            return {
                "facility": {
                    "id": obj.id,
                    "name": obj.name
                }
            }
        elif hasattr(obj, 'name'):  # Review group, Document, or other named objects
            # Determine the object type based on model name
            model_name = obj._meta.model_name.lower()
            if 'review' in model_name and 'group' in model_name:
                key = "review_group"
            elif 'document' in model_name:
                key = "document"
            else:
                key = "object"

            return {
                key: {
                    "id": obj.id,
                    "name": obj.name
                }
            }
        else:
            # Fallback for unknown object types
            return {
                "object": {
                    "id": obj.id,
                    "name": getattr(obj, 'name', str(obj))
                }
            }


# Legacy serializers for backward compatibility
class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'email', 'first_name', 'last_name', 'full_name']

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.email


class ActivityLogSerializer(serializers.ModelSerializer):
    """Legacy serializer for backward compatibility"""
    user = UserSerializer(read_only=True)

    class Meta:
        model = ActivityLog
        fields = [
            'id',
            'user',
            'activity_type',
            'timestamp',
            'description',
            'content_type',
            'object_id',
        ]
        read_only_fields = [
            'id',
            'timestamp',
            'content_type',
            'object_id',
        ]


class LegacyActivityLogCreateSerializer(serializers.ModelSerializer):
    """Legacy serializer for creating activity logs (backward compatibility)"""

    class Meta:
        model = ActivityLog
        fields = [
            'description',
        ]

    def create(self, validated_data):
        # This is kept for backward compatibility but should use the new service
        return super().create(validated_data)
