from django.test import TestCase
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from rest_framework.test import APIClient
from activities.models import ActivityLog, ActivityType
from activities.serializers import ActivityLogCreateSerializer, ActivityLogListSerializer
from general_patient_visitor.models import GeneralPatientVisitor
from base.models import Department, Facility


class ActivityLogCreateSerializerTest(TestCase):
    """Test cases for ActivityLogCreateSerializer"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St',
            phone_number='555-0123',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            description='Test department description',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_date='2023-10-04',
            incident_time='14:30:00',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )
        
        self.content_type = ContentType.objects.get_for_model(GeneralPatientVisitor)
        self.department_content_type = ContentType.objects.get_for_model(Department)

    def test_create_activity_with_basic_fields(self):
        """Test creating activity with basic required fields"""
        data = {
            'user': self.user.id,
            'activity_type': ActivityType.UPDATED,
            'description': 'Test activity',
            'content_type': self.content_type.id,
            'object_id': self.incident.id,
        }
        
        serializer = ActivityLogCreateSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        activity = serializer.save()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.UPDATED)
        self.assertEqual(activity.content_object, self.incident)

    def test_create_activity_with_highlight(self):
        """Test creating activity with highlight object"""
        data = {
            'user': self.user.id,
            'activity_type': ActivityType.SENT_TO_DEPARTMENT,
            'description': 'Sent to department',
            'content_type': self.content_type.id,
            'object_id': self.incident.id,
            'highlight_content_type': self.department_content_type.id,
            'highlight_object_id': self.department.id,
        }
        
        serializer = ActivityLogCreateSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        activity = serializer.save()
        self.assertEqual(activity.activity_highlight, self.department)

    def test_create_activity_with_files(self):
        """Test creating activity with files"""
        files_data = [
            {
                'document_url': 'https://example.com/file1.pdf',
                'document_id': 'doc123',
                'name': 'Report 1',
                'file_type': 'pdf'
            },
            {
                'document_url': 'https://example.com/file2.jpg',
                'document_id': 'doc124',
                'name': 'Image 1',
                'file_type': 'jpg'
            }
        ]
        
        data = {
            'user': self.user.id,
            'activity_type': ActivityType.DOCUMENT_ADDED,
            'description': '2 documents uploaded',
            'content_type': self.content_type.id,
            'object_id': self.incident.id,
            'files': files_data,
        }
        
        serializer = ActivityLogCreateSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        activity = serializer.save()
        self.assertEqual(activity.files, files_data)
        self.assertEqual(len(activity.files), 2)

    def test_validation_highlight_fields_together(self):
        """Test validation that highlight fields must be provided together"""
        # Test with only highlight_content_type
        data = {
            'user': self.user.id,
            'activity_type': ActivityType.SENT_TO_DEPARTMENT,
            'content_type': self.content_type.id,
            'object_id': self.incident.id,
            'highlight_content_type': self.department_content_type.id,
        }
        
        serializer = ActivityLogCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('highlight_object_id is required', str(serializer.errors))

    def test_invalid_activity_type(self):
        """Test validation with invalid activity type"""
        data = {
            'user': self.user.id,
            'activity_type': 'invalid_type',
            'content_type': self.content_type.id,
            'object_id': self.incident.id,
        }
        
        serializer = ActivityLogCreateSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('activity_type', serializer.errors)


class ActivityLogListSerializerTest(TestCase):
    """Test cases for ActivityLogListSerializer"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Bob',
            last_name='Williams'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St',
            phone_number='555-0123',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            name='Finance',
            description='Finance department',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_date='2023-10-04',
            incident_time='14:30:00',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )

    def test_serialize_activity_with_files(self):
        """Test serializing activity with files"""
        files_data = [
            {
                'document_url': 'https://example.com/file.pdf',
                'document_id': 'doc123',
                'name': 'Report',
                'file_type': 'pdf'
            }
        ]
        
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.DOCUMENT_ADDED,
            description='2 incoming documents uploaded',
            content_object=self.incident,
            files=files_data
        )
        
        serializer = ActivityLogListSerializer(activity)
        data = serializer.data
        
        self.assertEqual(data['id'], activity.id)
        self.assertEqual(data['incident'], self.incident.id)
        self.assertEqual(data['activity_message'], '2 incoming documents uploaded')
        self.assertEqual(data['files'], files_data)
        self.assertEqual(data['created_by']['id'], self.user.id)
        self.assertEqual(data['created_by']['first_name'], 'Bob')
        self.assertEqual(data['created_by']['last_name'], 'Williams')

    def test_serialize_activity_with_department_highlight(self):
        """Test serializing activity with department highlight"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.SENT_TO_DEPARTMENT,
            description='Incident sent for review to',
            content_object=self.incident,
            activity_highlight=self.department
        )

        serializer = ActivityLogListSerializer(activity)
        data = serializer.data

        expected_highlight = {
            'department': {
                'id': self.department.id,
                'name': 'Finance'
            }
        }
        expected_destination = {
            'department': {
                'id': self.department.id,
                'name': 'Finance'
            }
        }

        self.assertEqual(data['activity_highlight'], expected_highlight)
        self.assertEqual(data['destination'], expected_destination)

    def test_serialize_activity_without_highlight(self):
        """Test serializing activity without highlight returns empty dict"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.UPDATED,
            description='incident updated',
            content_object=self.incident
        )

        serializer = ActivityLogListSerializer(activity)
        data = serializer.data

        self.assertEqual(data['activity_highlight'], {})
        self.assertIsNone(data['destination'])

    def test_auto_generated_activity_message(self):
        """Test auto-generated activity messages"""
        # Test document message generation
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.DOCUMENT_ADDED,
            content_object=self.incident,
            files=[{'name': 'file1'}, {'name': 'file2'}]
        )
        
        serializer = ActivityLogListSerializer(activity)
        self.assertEqual(serializer.data['activity_message'], '2 incoming documents uploaded')
        
        # Test single document
        activity.files = [{'name': 'file1'}]
        activity.save()
        serializer = ActivityLogListSerializer(activity)
        self.assertEqual(serializer.data['activity_message'], '1 incoming document uploaded')
