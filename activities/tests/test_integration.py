"""
Integration tests to verify the new activity implementation works with 
staff incident reports and complaints services.
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from activities.models import ActivityLog, ActivityType
from activities.serializers import ActivityLogListSerializer
from staff_incident_reports.models import StaffIncidentReport
from complaints.models import Complaint
from base.models import Department, Facility
from activities.services import ActivityLogService


class ActivityIntegrationTest(TestCase):
    """Test integration of new activity system with existing services"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St',
            phone_number='555-0123',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            description='Test department',
            facility=self.facility
        )

    def test_staff_incident_report_creation_activity(self):
        """Test that creating a staff incident report logs the correct activity"""
        # Create incident directly to test activity logging
        incident = StaffIncidentReport.objects.create(
            report_facility=self.facility,
            department=self.department,
            incident_description='Test incident description',
            status='Draft',
            created_by=self.user
        )

        # Test the activity logging service directly

        ActivityLogService.create_activity(
            user=self.user,
            content_object=incident,
            activity_type=ActivityType.CREATED,
            description="Staff Incident Report created"
        )

        # Check that activity was logged
        content_type = ContentType.objects.get_for_model(StaffIncidentReport)
        activities = ActivityLog.objects.filter(
            content_type=content_type,
            object_id=incident.id
        )

        self.assertEqual(activities.count(), 1)
        activity = activities.first()
        self.assertEqual(activity.activity_type, ActivityType.CREATED)
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.content_object, incident)

    def test_staff_incident_report_update_activity(self):
        """Test that updating a staff incident report logs the correct activity"""
        # Create an incident
        incident = StaffIncidentReport.objects.create(
            report_facility=self.facility,
            department=self.department,
            incident_description='Original description',
            status='Draft',
            created_by=self.user
        )

        # Test activity logging for status change

        ActivityLogService.create_activity(
            user=self.user,
            content_object=incident,
            activity_type=ActivityType.STATUS_CHANGED,
            description="Status changed from Draft to Open"
        )

        ActivityLogService.create_activity(
            user=self.user,
            content_object=incident,
            activity_type=ActivityType.UPDATED,
            description="Staff Incident Report updated"
        )

        # Check that activities were logged
        content_type = ContentType.objects.get_for_model(StaffIncidentReport)
        activities = ActivityLog.objects.filter(
            content_type=content_type,
            object_id=incident.id
        ).order_by('timestamp')

        self.assertEqual(activities.count(), 2)

        # Check status change activity
        status_activity = activities.filter(activity_type=ActivityType.STATUS_CHANGED).first()
        self.assertIsNotNone(status_activity)
        self.assertEqual(status_activity.user, self.user)
        self.assertIn('Draft', status_activity.description)
        self.assertIn('Open', status_activity.description)

        # Check update activity
        update_activity = activities.filter(activity_type=ActivityType.UPDATED).first()
        self.assertIsNotNone(update_activity)
        self.assertEqual(update_activity.user, self.user)

    def test_staff_incident_report_document_activity(self):
        """Test that adding documents to staff incident report logs the correct activity"""
        incident = StaffIncidentReport.objects.create(
            report_facility=self.facility,
            department=self.department,
            incident_description='Test description',
            status='Draft',
            created_by=self.user
        )

        # Test document activity logging directly
        files_data = [
            {
                'document_url': 'https://example.com/test1.pdf',
                'document_id': 'doc1',
                'name': 'test1.pdf',
                'file_type': 'pdf'
            },
            {
                'document_url': 'https://example.com/test2.jpg',
                'document_id': 'doc2',
                'name': 'test2.jpg',
                'file_type': 'jpg'
            }
        ]

        ActivityLogService.create_activity(
            user=self.user,
            content_object=incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files_data
        )

        # Check that document activity was logged
        content_type = ContentType.objects.get_for_model(StaffIncidentReport)
        activities = ActivityLog.objects.filter(
            content_type=content_type,
            object_id=incident.id,
            activity_type=ActivityType.DOCUMENT_ADDED
        )

        self.assertEqual(activities.count(), 1)
        activity = activities.first()
        self.assertEqual(activity.user, self.user)
        self.assertIsNotNone(activity.files)
        self.assertEqual(len(activity.files), 2)

    def test_complaint_creation_activity(self):
        """Test that creating a complaint logs the correct activity"""
        # Create complaint directly
        complaint = Complaint.objects.create(
            patient_name='Test Patient',
            details='Test complaint description',
            complaint_nature='Service Issue',
            created_by=self.user
        )

        # Test activity logging directly
        ActivityLogService.create_activity(
            user=self.user,
            content_object=complaint,
            activity_type=ActivityType.CREATED,
            description="Complaint created"
        )

        # Check that activity was logged
        content_type = ContentType.objects.get_for_model(Complaint)
        activities = ActivityLog.objects.filter(
            content_type=content_type,
            object_id=complaint.id,
            activity_type=ActivityType.CREATED
        )

        self.assertEqual(activities.count(), 1)
        activity = activities.first()
        self.assertEqual(activity.user, self.user)

    def test_complaint_send_to_department_activity(self):
        """Test that sending complaint to department logs the correct activity"""
        # Create a complaint
        complaint = Complaint.objects.create(
            patient_name='Test Patient',
            details='Test complaint description',
            complaint_nature='Service Issue',
            created_by=self.user
        )

        # Test activity logging for sending to department
        ActivityLogService.create_activity(
            user=self.user,
            content_object=complaint,
            activity_type=ActivityType.SENT_TO_DEPARTMENT,
            highlight_object=self.department,
            destination_object=self.department
        )

        # Check that activity was logged
        content_type = ContentType.objects.get_for_model(Complaint)
        activities = ActivityLog.objects.filter(
            content_type=content_type,
            object_id=complaint.id,
            activity_type=ActivityType.SENT_TO_DEPARTMENT
        )

        self.assertEqual(activities.count(), 1)
        activity = activities.first()
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_highlight, self.department)

    def test_activity_serialization_format(self):
        """Test that activities are serialized in the correct format"""
        # Create a staff incident report
        incident = StaffIncidentReport.objects.create(
            report_facility=self.facility,
            department=self.department,
            incident_description='Test description',
            status='Draft',
            created_by=self.user
        )
        
        # Create activities using the new service
        
        # Document added activity
        files_data = [
            {
                'document_url': 'https://example.com/file.pdf',
                'document_id': 'doc123',
                'name': 'Test Document',
                'file_type': 'pdf'
            }
        ]
        
        ActivityLogService.create_activity(
            user=self.user,
            content_object=incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files_data
        )
        
        # Sent to department activity
        ActivityLogService.create_activity(
            user=self.user,
            content_object=incident,
            activity_type=ActivityType.SENT_TO_DEPARTMENT,
            highlight_object=self.department,
            destination_object=self.department
        )
        
        # Get activities and serialize
        content_type = ContentType.objects.get_for_model(StaffIncidentReport)
        activities = ActivityLog.objects.filter(
            content_type=content_type,
            object_id=incident.id
        ).order_by('-timestamp')
        
        serializer = ActivityLogListSerializer(activities, many=True)
        data = serializer.data
        
        self.assertEqual(len(data), 2)
        
        # Check document activity
        doc_activity = next(a for a in data if a['activity_type'] == ActivityType.DOCUMENT_ADDED)
        self.assertEqual(doc_activity['incident'], incident.id)
        self.assertEqual(doc_activity['files'], files_data)
        self.assertEqual(doc_activity['created_by']['id'], self.user.id)
        
        # Check department activity
        dept_activity = next(a for a in data if a['activity_type'] == ActivityType.SENT_TO_DEPARTMENT)
        self.assertEqual(dept_activity['incident'], incident.id)
        self.assertEqual(dept_activity['activity_highlight']['department']['id'], self.department.id)
        self.assertEqual(dept_activity['destination']['department']['id'], self.department.id)
