from datetime import datetime, timedelta
import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
import jwt
from rest_framework.test import APIClient
from rest_framework import status
from activities.models import ActivityLog, ActivityType
from activities.services import ActivityLogService
from core.settings import SECRET_KEY
from general_patient_visitor.models import GeneralPatientVisitor
from base.models import Facility, Department


class ActivityViewsTest(TestCase):
    """Test cases for Activity Views with new serializers"""

    def setUp(self):
        self.client = APIClient()
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='<PERSON>',
            last_name='<PERSON>'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St',
            phone_number='555-0123',
            email='<EMAIL>'
        )
        
        self.department = Department.objects.create(
            name='Finance',
            description='Finance department',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_type='General Patient/Visitor',
            status='Draft',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )
        
        # Create JWT token for authentication
        payload = {
            'user_id': self.user.id,
            'exp': datetime.now() + timedelta(days=1)
        }
        self.token = jwt.encode(payload, SECRET_KEY, algorithm='HS256')
        self.client.force_authenticate(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")
        self.auth_headers = {'Authorization': f'Bearer {self.token}'}

    def test_activities_list_with_document_added(self):
        """Test activities list endpoint with document_added activity"""
        files_data = [
            {
                'document_url': 'https://example.com/file.pdf',
                'document_id': 'doc123',
                'name': 'Report',
                'file_type': 'pdf'
            }
        ]
        
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files_data
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('data', data)
        self.assertIn('count', data)
        self.assertEqual(data['count'], 1)
        
        activity_data = data['data'][0]
        self.assertEqual(activity_data['incident'], self.incident.id)
        self.assertEqual(activity_data['activity_type'], ActivityType.DOCUMENT_ADDED)
        self.assertEqual(activity_data['files'], files_data)
        self.assertEqual(activity_data['created_by']['id'], self.user.id)
        self.assertEqual(activity_data['created_by']['first_name'], 'Bob')
        self.assertEqual(activity_data['created_by']['last_name'], 'Williams')

    def test_activities_list_with_sent_to_department(self):
        """Test activities list endpoint with sent_to_department activity"""
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.SENT_TO_DEPARTMENT,
            highlight_object=self.department
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        activity_data = data['data'][0]
        self.assertEqual(activity_data['incident'], self.incident.id)
        self.assertEqual(activity_data['activity_type'], ActivityType.SENT_TO_DEPARTMENT)
        
        expected_highlight = {
            'department': {
                'id': self.department.id,
                'name': 'Finance'
            }
        }
        expected_destination = {
            'department': {
                'id': self.department.id,
                'name': 'Finance'
            }
        }
        
        self.assertEqual(activity_data['activity_highlight'], expected_highlight)
        self.assertEqual(activity_data['destination'], expected_destination)

    def test_activities_list_with_review_added(self):
        """Test activities list endpoint with review_added activity"""
        target_user = User.objects.create_user(
            username='reviewer',
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Reviewer'
        )
        
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.REVIEW_ADDED,
            highlight_object=target_user
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        activity_data = data['data'][0]
        self.assertEqual(activity_data['incident'], self.incident.id)
        self.assertEqual(activity_data['activity_type'], ActivityType.REVIEW_ADDED)
        
        expected_highlight = {
            'user': {
                'id': target_user.id,
                'first_name': 'Jane',
                'last_name': 'Reviewer'
            }
        }
        
        self.assertEqual(activity_data['activity_highlight'], expected_highlight)

    def test_activities_list_with_updated(self):
        """Test activities list endpoint with updated activity"""
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        activity_data = data['data'][0]
        self.assertEqual(activity_data['incident'], self.incident.id)
        self.assertEqual(activity_data['activity_type'], ActivityType.UPDATED)
        self.assertEqual(activity_data['activity_message'], 'incident updated')
        self.assertEqual(activity_data['activity_highlight'], {})
        self.assertIsNone(activity_data['destination'])

    def test_activities_list_multiple_activities(self):
        """Test activities list with multiple activities in correct order"""
        # Create activities in sequence
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.CREATED
        )

        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED
        )

        files_data = [{'name': 'file1'}, {'name': 'file2'}]
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files_data
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['count'], 3)
        
        # Should be ordered by timestamp descending (newest first)
        activities = data['data']
        self.assertEqual(activities[0]['activity_type'], ActivityType.DOCUMENT_ADDED)
        self.assertEqual(activities[1]['activity_type'], ActivityType.UPDATED)
        self.assertEqual(activities[2]['activity_type'], ActivityType.CREATED)

    def test_activities_list_legacy_endpoint(self):
        """Test the legacy activities list endpoint still works"""
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.CREATED
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(url, **self.auth_headers)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('data', data)
        self.assertIn('count', data)
        self.assertEqual(data['count'], 1)

    def test_activities_list_unauthorized(self):
        """Test that unauthorized requests are rejected"""
        # Create a new client without authentication
        unauthorized_client = APIClient()
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = unauthorized_client.get(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_activities_list_empty_result(self):
        """Test activities list with no activities"""
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['count'], 0)
        self.assertEqual(data['data'], [])

    def test_activity_message_auto_generation(self):
        """Test that activity messages are auto-generated correctly"""
        # Test document message with count
        files_data = [{'name': 'file1'}, {'name': 'file2'}]
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files_data
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        data = response.json()
        activity_data = data['data'][0]
        self.assertEqual(activity_data['activity_message'], '2 incoming documents uploaded')

    def test_response_format_matches_specification(self):
        """Test that the response format exactly matches the specification"""
        files_data = [
            {
                'document_url': 'https://example.com/file.pdf',
                'document_id': 'doc123',
                'name': 'Report',
                'file_type': 'pdf'
            }
        ]
        
        ActivityLogService.create_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.DOCUMENT_ADDED,
            files=files_data,
            highlight_object=self.department
        )
        
        url = reverse('all_activities', kwargs={'incident_id': self.incident.id})
        response = self.client.get(
            url + '?incident_type=general_patient_visitor',
            **self.auth_headers
        )
        
        data = response.json()
        activity_data = data['data'][0]
        
        # Check all required fields are present
        required_fields = [
            'id', 'incident', 'activity_message', 'activity_highlight',
            'activity_date', 'destination', 'files', 'created_by', 'activity_type'
        ]
        
        for field in required_fields:
            self.assertIn(field, activity_data, f"Field '{field}' missing from response")
        
        # Check data types and structure
        self.assertIsInstance(activity_data['id'], int)
        self.assertIsInstance(activity_data['incident'], int)
        self.assertIsInstance(activity_data['activity_message'], str)
        self.assertIsInstance(activity_data['activity_highlight'], dict)
        self.assertIsInstance(activity_data['destination'], dict)
        self.assertIsInstance(activity_data['files'], list)
        self.assertIsInstance(activity_data['created_by'], dict)
