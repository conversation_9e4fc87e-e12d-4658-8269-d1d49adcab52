from typing import Optional, Dict, Any, List
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.db.models import Model, QuerySet
from activities.models import ActivityLog, ActivityType
from base.models import Department, Facility


class ActivityLogService:
    """
    Enhanced service for creating activity logs with intelligent field population
    based on activity_type.
    """

    @staticmethod
    def create_activity(
        user: Optional[User],
        content_object: Model,
        activity_type: str,
        description: Optional[str] = None,
        highlight_object: Optional[Model] = None,
        destination_object: Optional[Model] = None,
        files: Optional[List[Dict[str, Any]]] = None,
    ) -> ActivityLog:
        """
        Create an activity log with intelligent field population based on activity_type.

        Args:
            user: User who performed the action
            content_object: The incident object the activity is performed on
            activity_type: Type of activity (from ActivityType choices)
            description: Custom description (will be auto-generated if not provided)
            highlight_object: Object to highlight (User, Department, Facility, ReviewGroup, Document, etc.)
            destination_object: Destination object (can be different from highlight_object)
            files: List of file objects with document_url, document_id, name, file_type
            **kwargs: Additional fields

        Returns:
            ActivityLog: Created activity log instance
        """
        content_type = ContentType.objects.get_for_model(content_object)

        activity_data = {
            'user': user,
            'activity_type': activity_type,
            'content_type': content_type,
            'object_id': content_object.pk,
            'files': files,
        }

        if highlight_object:
            highlight_content_type = ContentType.objects.get_for_model(highlight_object)
            activity_data['highlight_content_type'] = highlight_content_type
            activity_data['highlight_object_id'] = highlight_object.pk

        if destination_object:
            destination_content_type = ContentType.objects.get_for_model(destination_object)
            activity_data['destination_content_type'] = destination_content_type
            activity_data['destination_object_id'] = destination_object.pk

        if not description:
            description = ActivityLogService._generate_activity_message(
                activity_type, files
            )
        activity_data['description'] = description


        return ActivityLog.objects.create(**activity_data)

    @staticmethod
    def _generate_activity_message(
        activity_type: str,
        files: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Generate activity message based on activity type and context"""

        if activity_type == ActivityType.DOCUMENT_ADDED:
            if files and isinstance(files, list):
                count = len(files)
                return f"{count} incoming document{'s' if count != 1 else ''} uploaded"
            return "document uploaded"

        elif activity_type in [
            ActivityType.SENT_TO_DEPARTMENT,
            ActivityType.SENT_TO_FACILITY,
            ActivityType.SENT_TO_REVIEWER,
            ActivityType.SENT_TO_REVIEW_GROUP
        ]:
            return "Incident sent for review to"

        elif activity_type == ActivityType.REVIEW_ADDED:
            return "review added"

        elif activity_type == ActivityType.UPDATED:
            return "incident updated"

        elif activity_type == ActivityType.CREATED:
            return "incident created"

        elif activity_type == ActivityType.STATUS_CHANGED:
            return "status changed"

        elif activity_type == ActivityType.ASSIGNED:
            return "incident assigned"

        elif activity_type == ActivityType.RESOLVED:
            return "incident resolved"

        else:
            return f"{activity_type.replace('_', ' ')} performed"


class ActivityService:
    """
    Centralized service for logging activities across all incident types.
    Provides a unified interface for tracking actions on incidents and complaints.
    """
    
    @staticmethod
    def log_activity(
        user: Optional[User],
        content_object: Model,
        activity_type: ActivityType,
        description: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        department: Optional[Department] = None,
        facility: Optional[Facility] = None,
        target_user: Optional[User] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> ActivityLog:
        """
        Log an activity for any incident or complaint type.
        
        Args:
            user: User who performed the action
            content_object: The incident/complaint object
            activity_type: Type of activity from ActivityType enum
            description: Custom description (optional, will be auto-generated if not provided)
            details: Additional metadata as JSON
            department: Department involved in the activity
            facility: Facility where the activity occurred
            target_user: User who is the target of this activity (for assignments, etc.)
            ip_address: IP address of the request
            user_agent: User agent string from the request
            
        Returns:
            ActivityLog: The created activity log entry
        """
        content_type = ContentType.objects.get_for_model(content_object)
        
        if not facility and hasattr(content_object, 'report_facility'):
            facility = content_object.report_facility
        elif not facility and hasattr(content_object, 'complain_facility'):
            facility = content_object.complain_facility
            
        if not department and hasattr(content_object, 'department'):
            department = content_object.department
        
        legacy_action = activity_type
        legacy_incident_id = str(content_object.pk) if content_object else None

        activity = ActivityLog.objects.create(
            user=user,
            activity_type=activity_type,
            action=legacy_action,
            description=description,
            details=details or {},
            content_type=content_type,
            object_id=content_object.pk,
            incident_id=legacy_incident_id,
            department=department,
            facility=facility,
            target_user=target_user,
            ip_address=ip_address,
            user_agent=user_agent,
        )
        
        return activity
    
    @staticmethod
    def log_creation(
        user: Optional[User],
        content_object: Model,
        **kwargs
    ) -> ActivityLog:
        """Log when an incident/complaint is created."""
        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=ActivityType.CREATED,
            description=f"Incident created",
            **kwargs
        )
    
    @staticmethod
    def log_update(
        user: Optional[User],
        content_object: Model,
        changed_fields: Optional[list] = None,
        **kwargs
    ) -> ActivityLog:
        """Log when an incident/complaint is updated."""
        details = kwargs.pop('details', {})
        if changed_fields:
            details['changed_fields'] = changed_fields

        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=ActivityType.UPDATED,
            description=f"Incident updated",
            details=details,
            **kwargs
        )
    
    @staticmethod
    def log_status_change(
        user: Optional[User],
        content_object: Model,
        old_status: str,
        new_status: str,
        **kwargs
    ) -> ActivityLog:
        """Log when an incident/complaint status changes."""
        details = kwargs.pop('details', {})
        details.update({
            'old_status': old_status,
            'new_status': new_status
        })

        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=ActivityType.STATUS_CHANGED,
            details=details,
            **kwargs
        )
    
    @staticmethod
    def log_department_transfer(
        user: Optional[User],
        content_object: Model,
        from_department: Optional[Department],
        to_department: Department,
        is_outgoing: bool = True,
        **kwargs
    ) -> ActivityLog:
        """Log when an incident is sent to/from a department."""
        activity_type = ActivityType.SENT_TO_DEPARTMENT if is_outgoing else ActivityType.SENT_FROM_DEPARTMENT

        details = kwargs.pop('details', {})
        details.update({
            'department_name': to_department.name if is_outgoing else from_department.name if from_department else 'Unknown',
            'from_department_id': from_department.id if from_department else None,
            'to_department_id': to_department.id if is_outgoing else None,
        })
        
        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=activity_type,
            department=to_department if is_outgoing else from_department,
            details=details,
            **kwargs
        )
    
    @staticmethod
    def log_document_activity(
        user: Optional[User],
        content_object: Model,
        document_count: int = 1,
        document_names: Optional[list] = None,
        is_addition: bool = True,
        **kwargs
    ) -> ActivityLog:
        """Log when documents are added or removed."""
        activity_type = ActivityType.DOCUMENT_ADDED if is_addition else ActivityType.DOCUMENT_REMOVED

        details = kwargs.pop('details', {})
        details.update({
            'document_count': document_count,
            'document_names': document_names or []
        })
        
        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=activity_type,
            details=details,
            **kwargs
        )
    
    @staticmethod
    def log_assignment(
        user: Optional[User],
        content_object: Model,
        assignee: User,
        is_assignment: bool = True,
        **kwargs
    ) -> ActivityLog:
        """Log when an incident is assigned or unassigned to a single user."""
        activity_type = ActivityType.ASSIGNED if is_assignment else ActivityType.UNASSIGNED

        details = kwargs.pop('details', {})
        details.update({
            'assignee_name': f"{assignee.first_name} {assignee.last_name}".strip() or assignee.email,
            'assignee_id': assignee.id
        })

        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=activity_type,
            target_user=assignee,
            details=details,
            **kwargs
        )

    @staticmethod
    def log_multiple_assignments(
        user: Optional[User],
        content_object: Model,
        assignees: list,
        is_assignment: bool = True,
        **kwargs
    ) -> list:
        """Log when an incident is assigned or unassigned to multiple users."""
        activities = []
        total_assignees = len(assignees)

        for assignee in assignees:
            details = kwargs.get('details', {}).copy()
            details.update({
                'assignee_name': f"{assignee.first_name} {assignee.last_name}".strip() or assignee.email,
                'assignee_id': assignee.id,
                'total_assignees': total_assignees,
                'assignment_type': 'multiple' if total_assignees > 1 else 'single'
            })

            activity = ActivityService.log_assignment(
                user=user,
                content_object=content_object,
                assignee=assignee,
                is_assignment=is_assignment,
                details=details,
                **{k: v for k, v in kwargs.items() if k != 'details'}
            )
            activities.append(activity)

        return activities
    
    @staticmethod
    def log_review_activity(
        user: Optional[User],
        content_object: Model,
        review_type: str = "review",
        **kwargs
    ) -> ActivityLog:
        """Log when a review is added."""
        details = kwargs.pop('details', {})
        details.update({
            'review_type': review_type
        })

        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=ActivityType.REVIEW_ADDED,
            details=details,
            **kwargs
        )
    
    @staticmethod
    def log_resolution(
        user: Optional[User],
        content_object: Model,
        is_resolved: bool = True,
        **kwargs
    ) -> ActivityLog:
        """Log when an incident is resolved or reopened."""
        activity_type = ActivityType.RESOLVED if is_resolved else ActivityType.REOPENED
        
        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=activity_type,
            **kwargs
        )
    
    @staticmethod
    def log_investigation_activity(
        user: Optional[User],
        content_object: Model,
        is_started: bool = True,
        **kwargs
    ) -> ActivityLog:
        """Log investigation start or completion."""
        activity_type = ActivityType.INVESTIGATION_STARTED if is_started else ActivityType.INVESTIGATION_COMPLETED
        
        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=activity_type,
            **kwargs
        )
    
    @staticmethod
    def log_workflow_step(
        user: Optional[User],
        content_object: Model,
        step_name: str,
        **kwargs
    ) -> ActivityLog:
        """Log when a workflow step is completed."""
        details = kwargs.pop('details', {})
        details.update({
            'step_name': step_name
        })
        
        return ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=ActivityType.WORKFLOW_STEP_COMPLETED,
            details=details,
            **kwargs
        )
    
    @staticmethod
    def get_activities_for_object(content_object: Model) -> 'QuerySet[ActivityLog]':
        """Get all activities for a specific incident/complaint object."""
        content_type = ContentType.objects.get_for_model(content_object)
        return ActivityLog.objects.filter(
            content_type=content_type,
            object_id=content_object.pk
        ).order_by('-timestamp')
