from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from base.constants import ReviewStatus
from base.services.incidents.base import IncidentService
from base.services.responses import APIResponse
from staff_incident_reports.models import StaffIncidentReport
from base.services.logging.logger import LoggingService
from staff_incident_reports.new_serializers import GetStaffIncidentReportSerializer
from staff_incident_reports.serializers import ModifyReportVersionSerializer
from incidents.services.workflow import IncidentWorkflow
from rest_framework import status
from django.core.exceptions import ValidationError
from incidents.views.send_to_department import send_incident_submission_email
from activities.services import ActivityLogService
from activities.models import ActivityType


logging_service = LoggingService()
user_profile_service = UserProfileService()


class StaffIncidentReportActionService:
    """
    Service class for handling Staff Incident Report incident actions.
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(
            model=StaffIncidentReport, user=self.user
        )
        self.general_incident = IncidentService()
        try:
            self.incident = (
                StaffIncidentReport.objects.select_related(
                    "report_facility",
                    "department",
                    "patient_info",
                    "supervisor",
                    "doctor_consulted_info",
                )
                .prefetch_related(
                    "witnesses",
                    "reviews",
                    "documents",
                )
                .get(id=incident_id)
            )
        except StaffIncidentReport.DoesNotExist:
            self.incident = None

    def modify_incident(self) -> APIResponse:
        """Modifies an existing Staff Report Incident."""
        try:
            report_facility = self.incident.report_facility
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, self.incident.report_facility)
                and not is_manager_user(self.user, self.incident.department)
            ) and not self.incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    message="You do not have permission to modify this incident",
                    code=status.HTTP_403_FORBIDDEN,
                    data=None
                )

            if "patient_info" in self.data:
                patient_profile = user_profile_service.get_or_create_profile(
                    self.data["patient_info"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["patient_info"] = patient_profile.data.id

            if "supervisor" in self.data:
                supervisor = user_profile_service.get_or_create_profile(
                    self.data["supervisor"]
                )
                if not supervisor.success:
                    return APIResponse(
                        success=False,
                        message=supervisor.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["supervisor"] = supervisor.data.id

            if "doctor_consulted_info" in self.data:
                doctor_consulted = user_profile_service.get_or_create_profile(
                    self.data["doctor_consulted_info"]
                )
                if not doctor_consulted.success:
                    return APIResponse(
                        success=False,
                        data=None,
                        code=status.HTTP_400_BAD_REQUEST,
                        message=doctor_consulted.message,
                    )
                self.data["doctor_consulted_info"] = doctor_consulted.data.id
            
            if "doctor_info" in self.data:
                doctor_info = user_profile_service.get_or_create_profile(
                    self.data["doctor_info"]
                )
                if not doctor_info.success:
                    return APIResponse(
                        success=False,
                        message=doctor_info.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["doctor_info"] = doctor_info.data.id
            
            if " name_of_injured_staff" in self.data:
                name_of_injured_staff = user_profile_service.get_or_create_profile(
                    self.data["name_of_injured_staff"]
                )
                if not name_of_injured_staff.success:
                    return APIResponse(
                        success=False,
                        message=name_of_injured_staff.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None
                    )
                self.data["name_of_injured_staff"] = name_of_injured_staff.data.id
            
            if "witnesses" in self.data:
                witness_ids = []
                for witness_data in self.data["witnesses"]:
                    profile = user_profile_service.get_or_create_profile(witness_data)
                    if not profile.success:
                        return APIResponse(
                            success=False,
                            message=f"Witness profile error: {profile.message}",
                            code=status.HTTP_400_BAD_REQUEST,
                            data=None
                        )
                    witness_ids.append(profile.data.id)
                self.data["witnesses"] = witness_ids

            self.data["report_facility"] = report_facility
            self.data["department"] = self.incident.department.id
            self.data["created_by"] = self.incident.created_by.id
            self.data["original_report"] = self.incident.id

            version_serializer = ModifyReportVersionSerializer(
                data=self.data
            )
            if version_serializer.is_valid():
                version_serializer.save()
                old_status = self.incident.status
                self.incident.is_modified = True
                self.incident.updated_by = self.user
                self.incident.status = self.data.get("status", ReviewStatus.DRAFT)
                self.incident.save()

                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=self.incident,
                    activity_type=ActivityType.UPDATED,
                    description="Incident modified"
                )

                new_status = self.incident.status
                if old_status != new_status:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=self.incident,
                        activity_type=ActivityType.STATUS_CHANGED,
                        description=f"Status changed from {old_status} to {new_status}"
                    )

                if self.incident.status == ReviewStatus.OPEN:
                    send_incident_submission_email(
                        incident=self.incident,
                        incident_type="Staff Incident Report",
                    )
                return APIResponse(
                    success=True,
                    message="Staff Incident Report modified successfully",
                    code=status.HTTP_200_OK,
                    data=version_serializer.data
                )
            else:
                logging_service.log_error(version_serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data provided",
                    code=status.HTTP_400_BAD_REQUEST,
                    data=version_serializer.errors
                )
        except StaffIncidentReport.DoesNotExist:
            return APIResponse(
                success=False,
                message="Staff Incident Report not found",
                code=status.HTTP_404_NOT_FOUND,
                data=None
            )
        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                code=status.HTTP_400_BAD_REQUEST,
                data=None
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error modifying incident",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )

    def send_for_review(self) -> APIResponse:
        """Sends the Staff Incident Report for review."""
        try:
            response = self.workflow_services.send_for_a_review(
                self.incident_id,
                self.data
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None
                )

            assignees = response.data.assignees.all()
            if assignees.exists():
                for assignee in assignees:
                    ActivityLogService.create_activity(
                        user=self.user,
                        content_object=response.data,
                        activity_type=ActivityType.SENT_TO_REVIEWER,
                        highlight_object=assignee,
                        destination_object=assignee
                    )
            else:
                ActivityLogService.create_activity(
                    user=self.user,
                    content_object=response.data,
                    activity_type=ActivityType.SENT_TO_REVIEWER
                )

            serializer = GetStaffIncidentReportSerializer(response.data)
            return APIResponse(
                success=True,
                message="Staff Incident Report sent for review",
                code=status.HTTP_200_OK,
                data=serializer.data
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error sending incident for review",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )

    def mark_closed(self) -> APIResponse:
        """Marks the Staff Incident Report as resolved."""
        try:
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident,
                user=self.user
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.RESOLVED,
                description="Incident marked as resolved"
            )

            return APIResponse(
                success=True,
                message="Staff Incident Report marked as resolved",
                code=status.HTTP_200_OK,
                data=None
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error marking incident as resolved",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )

    def delete_staff_incident_draft_incidents(self) -> APIResponse:
        """Deletes draft incidents for Staff Incident Report."""
        try:
            response = self.workflow_services.delete_drafts(
                user=self.user,
                model=StaffIncidentReport,
                incident_id=self.data.get("incident_ids", None),
            )
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None
                )

            ActivityLogService.create_activity(
                user=self.user,
                content_object=self.incident,
                activity_type=ActivityType.DELETED,
                description="Draft incident deleted"
            )

            return APIResponse(
                success=True,
                message="Draft incidents deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Error deleting draft incidents",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None
            )

